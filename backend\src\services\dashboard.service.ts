// Dashboard service - Business logic
import { PrismaClient, OrderStatus, PaymentStatus } from '../generated/client';
import { 
  DashboardData, 
  DashboardMetrics, 
  PopularProduct, 
  OutOfStockProduct, 
  OrderSummary,
  OrderItemSummary,
  DateRange,
  MetricComparison,
  ORDER_STATUS_DESCRIPTIONS,
  ORDER_ITEM_STATUS_DESCRIPTIONS,
  DashboardServiceInterface
} from '../types/dashboard.types';
import { ErrorHandler } from '../middlewares/error.middleware';

export class DashboardService implements DashboardServiceInterface {
  constructor(private prisma: PrismaClient) {}

  /**
   * Tüm dashboard verilerini getirir
   */
  async getDashboardData(branchId: string): Promise<DashboardData> {
    try {
      // Branch bilgisini al
      const branch = await this.prisma.branch.findUnique({
        where: { id: branchId },
        select: { name: true }
      });

      if (!branch) {
        throw ErrorHandler.createError('<PERSON><PERSON> bulunamadı', 404, 'BRANCH_NOT_FOUND');
      }

      // Paralel olarak tüm verileri getir
      const [
        metrics,
        popularProducts,
        outOfStockProducts,
        ordersInProgress,
        ordersWaitingPayment
      ] = await Promise.all([
        this.getMetrics(branchId),
        this.getPopularProducts(branchId, 5),
        this.getOutOfStockProducts(branchId),
        this.getOrdersInProgress(branchId),
        this.getOrdersWaitingPayment(branchId)
      ]);

      return {
        metrics,
        popularProducts,
        outOfStockProducts,
        ordersInProgress,
        ordersWaitingPayment,
        lastUpdated: new Date(),
        branchId,
        branchName: branch.name
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Dashboard metriklerini hesaplar
   */
  async getMetrics(branchId: string): Promise<DashboardMetrics> {
    try {
      const today = this.getTodayDateRange();
      const yesterday = this.getYesterdayDateRange();

      // Bugünün verilerini al
      const todayData = await this.getMetricsForDateRange(branchId, today);
      
      // Dünün verilerini al
      const yesterdayData = await this.getMetricsForDateRange(branchId, yesterday);

      return {
        dailyRevenue: {
          amount: todayData.revenue,
          changeFromYesterday: this.calculatePercentageChange(
            todayData.revenue, 
            yesterdayData.revenue
          )
        },
        ordersInProgress: {
          count: todayData.ordersInProgress,
          changeFromYesterday: this.calculatePercentageChange(
            todayData.ordersInProgress,
            yesterdayData.ordersInProgress
          )
        },
        ordersWaitingPayment: {
          count: todayData.ordersWaitingPayment,
          changeFromYesterday: this.calculatePercentageChange(
            todayData.ordersWaitingPayment,
            yesterdayData.ordersWaitingPayment
          )
        },
        totalOrdersToday: todayData.totalOrders,
        averageOrderValue: todayData.totalOrders > 0 
          ? todayData.revenue / todayData.totalOrders 
          : 0
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Popüler ürünleri getirir
   */
  async getPopularProducts(branchId: string, limit: number = 5): Promise<PopularProduct[]> {
    try {
      const today = this.getTodayDateRange();

      const popularProducts = await this.prisma.orderItem.groupBy({
        by: ['productId'],
        where: {
          order: {
            branchId,
            orderedAt: {
              gte: today.startDate,
              lte: today.endDate
            },
            status: {
              not: OrderStatus.CANCELLED
            }
          }
        },
        _sum: {
          quantity: true,
          totalAmount: true
        },
        _count: {
          orderId: true
        },
        orderBy: {
          _sum: {
            quantity: 'desc'
          }
        },
        take: limit
      });

      // Ürün detaylarını getir
      const productDetails = await Promise.all(
        popularProducts.map(async (item) => {
          const product = await this.prisma.product.findUnique({
            where: { id: item.productId },
            include: {
              category: {
                select: { name: true }
              }
            }
          });

          return {
            id: item.productId,
            name: product?.name || 'Bilinmeyen Ürün',
            categoryName: product?.category?.name || 'Kategori Yok',
            totalQuantity: Number(item._sum.quantity || 0),
            totalOrders: item._count.orderId,
            revenue: Number(item._sum.totalAmount || 0),
            image: product?.image || undefined
          };
        })
      );

      return productDetails;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Stok dışı ürünleri getirir
   */
  async getOutOfStockProducts(branchId: string): Promise<OutOfStockProduct[]> {
    try {
      const outOfStockProducts = await this.prisma.product.findMany({
        where: {
          available: false,
          company: {
            branches: {
              some: {
                id: branchId
              }
            }
          }
        },
        include: {
          category: {
            select: { name: true }
          }
        },
        take: 10 // Maksimum 10 ürün göster
      });

      return outOfStockProducts.map(product => ({
        id: product.id,
        name: product.name,
        categoryName: product.category?.name || 'Kategori Yok',
        estimatedRestockTime: this.getEstimatedRestockTime(product.id),
        image: product.image || undefined
      }));
    } catch (error) {
      throw error;
    }
  }

  /**
   * İşlemdeki siparişleri getirir
   */
  async getOrdersInProgress(branchId: string): Promise<OrderSummary[]> {
    try {
      const orders = await this.getOrdersByStatus(branchId, [
        OrderStatus.CONFIRMED,
        OrderStatus.PREPARING,
        OrderStatus.READY
      ]);

      return orders;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Ödeme bekleyen siparişleri getirir
   */
  async getOrdersWaitingPayment(branchId: string): Promise<OrderSummary[]> {
    try {
      const orders = await this.prisma.order.findMany({
        where: {
          branchId,
          status: OrderStatus.COMPLETED,
          paymentStatus: {
            in: [PaymentStatus.UNPAID, PaymentStatus.PARTIAL]
          }
        },
        include: {
          table: {
            select: { number: true }
          },
          items: {
            include: {
              product: {
                select: { name: true }
              }
            }
          }
        },
        orderBy: {
          orderedAt: 'asc'
        },
        take: 20
      });

      return orders.map(order => this.mapOrderToSummary(order));
    } catch (error) {
      throw error;
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  private async getMetricsForDateRange(branchId: string, dateRange: DateRange) {
    const [revenue, ordersInProgress, ordersWaitingPayment, totalOrders] = await Promise.all([
      // Günlük kazanç (tamamlanan siparişler)
      this.prisma.order.aggregate({
        where: {
          branchId,
          status: OrderStatus.COMPLETED,
          orderedAt: {
            gte: dateRange.startDate,
            lte: dateRange.endDate
          }
        },
        _sum: {
          totalAmount: true
        }
      }),

      // İşlemdeki siparişler
      this.prisma.order.count({
        where: {
          branchId,
          status: {
            in: [OrderStatus.CONFIRMED, OrderStatus.PREPARING, OrderStatus.READY]
          },
          orderedAt: {
            gte: dateRange.startDate,
            lte: dateRange.endDate
          }
        }
      }),

      // Ödeme bekleyen siparişler
      this.prisma.order.count({
        where: {
          branchId,
          status: OrderStatus.COMPLETED,
          paymentStatus: {
            in: [PaymentStatus.UNPAID, PaymentStatus.PARTIAL]
          },
          orderedAt: {
            gte: dateRange.startDate,
            lte: dateRange.endDate
          }
        }
      }),

      // Toplam sipariş sayısı
      this.prisma.order.count({
        where: {
          branchId,
          status: {
            not: OrderStatus.CANCELLED
          },
          orderedAt: {
            gte: dateRange.startDate,
            lte: dateRange.endDate
          }
        }
      })
    ]);

    return {
      revenue: Number(revenue._sum.totalAmount || 0),
      ordersInProgress,
      ordersWaitingPayment,
      totalOrders
    };
  }

  private getTodayDateRange(): DateRange {
    const now = new Date();
    const startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
    
    return { startDate, endDate };
  }

  private getYesterdayDateRange(): DateRange {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const startDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
    const endDate = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
    
    return { startDate, endDate };
  }

  private calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) {
      return current > 0 ? 100 : 0;
    }
    return Math.round(((current - previous) / previous) * 100);
  }

  private getEstimatedRestockTime(productId: string): string {
    // Şimdilik sabit değer, gelecekte dinamik olacak
    return 'Bilinmiyor';
  }

  private async getOrdersByStatus(branchId: string, statuses: OrderStatus[]): Promise<OrderSummary[]> {
    const orders = await this.prisma.order.findMany({
      where: {
        branchId,
        status: {
          in: statuses
        }
      },
      include: {
        table: {
          select: { number: true }
        },
        items: {
          include: {
            product: {
              select: { name: true }
            }
          }
        }
      },
      orderBy: {
        orderedAt: 'asc'
      },
      take: 20
    });

    return orders.map(order => this.mapOrderToSummary(order));
  }

  private mapOrderToSummary(order: any): OrderSummary {
    const items: OrderItemSummary[] = order.items.map((item: any) => ({
      id: item.id,
      productName: item.product.name,
      quantity: Number(item.quantity),
      status: item.status,
      statusDescription: ORDER_ITEM_STATUS_DESCRIPTIONS[item.status],
      note: item.note || undefined
    }));

    return {
      id: order.id,
      orderNumber: order.orderNumber,
      customerName: order.customerName || undefined,
      tableNumber: order.table?.number?.toString() || undefined,
      itemCount: order.items.length,
      totalAmount: Number(order.totalAmount),
      status: order.status,
      paymentStatus: order.paymentStatus,
      orderedAt: order.orderedAt,
      estimatedReadyTime: this.calculateEstimatedReadyTime(order),
      statusDescription: ORDER_STATUS_DESCRIPTIONS[order.status],
      items
    };
  }

  private calculateEstimatedReadyTime(order: any): Date | undefined {
    // Şimdilik basit hesaplama, gelecekte ürün hazırlık sürelerine göre hesaplanacak
    if (order.status === OrderStatus.PREPARING) {
      const estimatedTime = new Date(order.orderedAt);
      estimatedTime.setMinutes(estimatedTime.getMinutes() + 15); // 15 dakika varsayılan
      return estimatedTime;
    }
    return undefined;
  }
}
