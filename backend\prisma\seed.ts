// Seed script - Test verileri oluşturur
import { PrismaClient, UserRole } from '../src/generated/client';
import { HashUtils } from '../src/utils/hash.utils';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Test şirketi oluştur
  const company = await prisma.company.upsert({
    where: { taxNumber: '1234567890' },
    update: {},
    create: {
      name: 'Test Restaurant',
      taxNumber: '1234567890',
      taxOffice: 'Test Vergi Dairesi',
      address: 'Test Adres',
      phone: '0212 123 45 67',
      email: '<EMAIL>',
    },
  });

  console.log('✅ Company created:', company.name);

  // Test şubesi oluştur
  const branch = await prisma.branch.upsert({
    where: { 
      companyId_code: {
        companyId: company.id,
        code: 'MAIN'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      code: '<PERSON><PERSON>',
      name: '<PERSON>',
      address: '<PERSON>',
      phone: '0212 123 45 67',
      email: '<EMAIL>',
      isMainBranch: true,
      openingTime: '09:00',
      closingTime: '23:00',
    },
  });

  console.log('✅ Branch created:', branch.name);

  // Test çalışanları oluştur
  const employees = [
    {
      username: 'manager1',
      firstName: 'Ahmet',
      lastName: 'Yılmaz',
      role: UserRole.BRANCH_MANAGER,
      pin: '123456',
    },
    {
      username: 'cashier1',
      firstName: 'Ayşe',
      lastName: 'Demir',
      role: UserRole.CASHIER,
      pin: '567856',
    },
    {
      username: 'waiter1',
      firstName: 'Mehmet',
      lastName: 'Kaya',
      role: UserRole.WAITER,
      pin: '999956',
    },
    {
      username: 'kitchen1',
      firstName: 'Fatma',
      lastName: 'Özkan',
      role: UserRole.KITCHEN,
      pin: '11116',
    },
  ];

  for (const emp of employees) {
    const hashedPin = await HashUtils.hashPin(emp.pin);
    
    const user = await prisma.user.upsert({
      where: { username: emp.username },
      update: {},
      create: {
        companyId: company.id,
        branchId: branch.id,
        username: emp.username,
        password: await HashUtils.hashText('password123'), // Default password
        pin: hashedPin,
        firstName: emp.firstName,
        lastName: emp.lastName,
        role: emp.role,
        active: true,
      },
    });

    console.log(`✅ Employee created: ${user.firstName} ${user.lastName} (${user.role}) - PIN: ${emp.pin}`);
  }

  // Test vergi oranları oluştur
  const taxes = [
    { name: 'KDV %8', rate: 8, code: 'VAT8' },
    { name: 'KDV %18', rate: 18, code: 'VAT18', isDefault: true },
  ];

  for (const tax of taxes) {
    await prisma.tax.upsert({
      where: {
        companyId_code: {
          companyId: company.id,
          code: tax.code,
        },
      },
      update: {},
      create: {
        companyId: company.id,
        name: tax.name,
        rate: tax.rate,
        code: tax.code,
        isDefault: tax.isDefault || false,
      },
    });
  }

  console.log('✅ Tax rates created');

  // Test ödeme yöntemleri oluştur
  const paymentMethods = [
    { name: 'Nakit', code: 'CASH', type: 'CASH' },
    { name: 'Kredi Kartı', code: 'CC', type: 'CREDIT_CARD' },
    { name: 'Yemek Kartı', code: 'MEAL', type: 'MEAL_CARD' },
  ];

  for (const pm of paymentMethods) {
    await prisma.paymentMethod.upsert({
      where: {
        companyId_code: {
          companyId: company.id,
          code: pm.code,
        },
      },
      update: {},
      create: {
        companyId: company.id,
        name: pm.name,
        code: pm.code,
        type: pm.type as any,
      },
    });
  }

  console.log('✅ Payment methods created');

  console.log('🎉 Seeding completed successfully!');
  console.log('\n📋 Test Accounts:');
  console.log('Manager: manager1 / PIN: 1234');
  console.log('Cashier: cashier1 / PIN: 5678');
  console.log('Waiter: waiter1 / PIN: 9999');
  console.log('Kitchen: kitchen1 / PIN: 1111');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
